@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
<style type="text/css">
    .val-error
    {
        color:red;
    }
 </style>
 @include('backend.user-pages.districts.create')
 @include('backend.user-pages.districts.edit')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    @if(auth()->user()->pk_int_user_id != 901)
                    <h5>DISTRICTS</h5>
                    @else
                    <h5>Zone</h5>
                    @endif
                </div>
                <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                            @if(auth()->user()->pk_int_user_id != 901)
                                <li class=""><a class="main-round-btn bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#add_districts" id="add_district" href="#">Add Districts</a>
                                </li>
                                @else
                                <li class=""><a class="main-round-btn bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#add_districts" id="add_district" href="#">Add Zone</a>
                                </li>
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.crmsidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <div class="col-md-12" style="overflow: auto">
                <table id="district-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Sl No</th>
                            @if(auth()->user()->pk_int_user_id != 901)
                            <th>Disticts</th>
                            @else
                            <th>Zone</th>
                            @endif
                            <th width="10%">Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
            </div>
        </div>
    </main>

@endsection 
@push('footer.script')
<script type="text/javascript">

    $(document).ready(function(){
        $('#add_spin').hide();
        $('#edit_spin').hide();
        
        BASE_URL ={!! json_encode(url('/')) !!}
      
        $('#district-table').DataTable({
           scrollX: true,
           paging: true,
            language: {
                searchPlaceholder: 'Search',
                sSearch: '',
                lengthMenu: '_MENU_ page',
            },
            columnDefs: [{
                    "width": "140px",
                    "targets": [2]
                },
            ],
            ajax: BASE_URL + '/user/get-all-districts',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
        });

// setting values for edit popup
        $(document).on('click', '.edit-btn', function () {
            $('#edit_spin').hide();
            $('#id').val($(this).data('id'));
            $('#name').val($(this).data('name'));
            $('#status').val($(this).data('status'));
        });
// ends

// adding dlt template data to data base
        $(document).on('submit', '#Add', function(event) {
            $('#add_spin').show();
            event.preventDefault();
            $('.val-error').html('');
            $('.val-error').hide();
                $.ajax({
                    url: BASE_URL + '/user/add-district',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                        $('#add_spin').hide();
                        $("#Add")[0].reset();
                        $('#add_districts').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    }else{
                        $('#add_spin').hide();
                        $.each(res.msg, function(index, val) {
                          //iterate through array or object
                          $('.'+index).html(val);
                          $('.'+index).show();
                        });
                    }
                })
                .fail(function() {
                })
                .always(function(com) {
                   $('#district-table').DataTable().ajax.reload(null, false);
                });
            });
// end
        $('.error').hide();
        $('.ks-izi-modal-trigger').on('click', function (e) {
           $('.error').html('');
           $('.error').hide();
        });

// update data
        $(document).on('submit', '#Edit', function(event) {
            $('#edit_spin').show();
            event.preventDefault();
            $('.error').html('');
            $('.error').hide();
                $.ajax({
                    url: BASE_URL + '/user/district/update',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                        $('#edit_spin').hide();
                        $('#district_edit').modal('toggle');
                        $.alert({
                             title: 'Success',
                             type: 'green',
                             content: res.msg,
                         });
                    }else{
                        $('#edit_spin').hide();
                        $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    }
                })
                .fail(function() {
                })
                .always(function(com) {
                    $('#district-table').DataTable().ajax.reload(null, false);

                });
        });
// ends

// delete sms dlt data
        $('#district-table').on('click', '.district-delete', function (event) {
            event.preventDefault();
            var deleteUrl = BASE_URL + '/user/delete-district/' + $(this).data('id');
          
            $.confirm({
                title: 'Deletion',
                content: 'Are you sure you want to delete ?',
                icon: 'la la-question-circle',
                animation: 'scale',
                closeAnimation: 'scale',
                opacity: 0.5,
                buttons: {
                   'confirm': {
                       text: 'Proceed',
                       btnClass: 'btn-info',
                       action: function () {
                            $.ajax({
                                url: deleteUrl,
                                type: 'DELETE',
                            })
                            .done(function(res) {
                                if(res.status == 'success') {
                                    $.alert({
                                        title: 'Success',
                                        type: 'green',
                                        content: res.msg,
                                    });
                                } else {
                                    $.alert({
                                        title: 'Failed',
                                        type: 'red',
                                        content: res.msg,
                                    });
                                }
                            })
                            .fail(function(err) {
                            })
                            .always(function(com) {
                                $('#district-table').DataTable().ajax.reload(null, false);
                            });
                        }
                    },
                    cancel: function () {
                        $.alert('Operation <strong>canceled</strong>');
                    }
                }
            });
        });
//ends

    });
</script>
@endpush
