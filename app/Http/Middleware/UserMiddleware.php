<?php

namespace App\Http\Middleware;

use Closure;
use Auth;
use App\Common\Variables;
use Session;

class UserMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if ($request->ajax() && Auth::check() && (Auth::user()->int_role_id == Variables::USER || Auth::user()->int_role_id == Variables::STAFF)) {
            return $next($request);
        } elseif ($request->ajax() && Auth::check() && Auth::user()->int_role_id != Variables::USER) {
            return response(['msg' => 'invalid request', 'status' => 'fail']);
        } elseif ($request->ajax()){
            return response(['msg' => 'invalid request', 'status' => 'fail']);
        }

        if (Auth::check() && (Auth::user()->int_role_id == Variables::USER || Auth::user()->int_role_id >= Variables::STAFF)  && (Auth::user()->int_role_id != Variables::ROLE_ADMIN)) {
            return $next($request);
        } elseif (!Auth::check()) {
            $url = Request()->path();
            Session::put('loginRedirect', $url);
            return redirect('/login');
        } else {
            return redirect('/admin/dashboard');
        }

        return back();
    }
}
