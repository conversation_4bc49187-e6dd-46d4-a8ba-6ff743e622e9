<?php

namespace App\Http\Controllers\GlCRM\User\Leads;

use App\BackendModel\EnquiryType;
use App\BackendModel\Settings;
use App\Common\Variables;
use App\FrontendModel\LeadAdditionalField;
use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DataTables;
use Validator;
use Auth;

class EnquirySettingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $settings = Settings::where('fk_int_user_id',User::getVendorId())->get();
        return view('gl-crm.pages.user.leads.enquiry-settings.index',compact('settings'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
       if(request()->has('label')){
        switch (request('label')) {
            case 'lead_prefix':
                $settings = Settings::where('vchr_settings_type','Lead Prefix')->where('fk_int_user_id',User::getVendorId())->first();

                if($settings){
                    $settings->vchr_settings_value = strtoupper(request('value'));
                    $settings->save();
                }else{
                    $settings = new Settings();
                    $settings->vchr_settings_type = 'Lead Prefix';
                    $settings->vchr_settings_value = strtoupper(request('value'));
                    $settings->fk_int_user_id = User::getVendorId();
                    $settings->save();
                }

                break;
            
            default:
                # code...
                break;
        }
        return response()->json(['status'=>'success','msg' => 'Settings updated']);
       }else{
        return response()->json(['status'=>'error','msg' => 'Settings updation failed']);
       }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
       //
    }
}
