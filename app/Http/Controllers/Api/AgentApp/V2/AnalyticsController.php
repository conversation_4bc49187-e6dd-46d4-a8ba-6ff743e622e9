<?php

namespace App\Http\Controllers\Api\AgentApp\V2;

use App\User;
use App\AgentStaff;
use App\BackendModel\Enquiry;
use App\Common\Application;
use App\Common\Variables;
use App\Deal;
use App\DealTask;
use App\FrontendModel\LeadAdditionalField;
use Illuminate\Http\Request;
use App\Http\Controllers\Api\AgentApp\Controller;
use App\Task;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AnalyticsController extends Controller
{
    protected $userId;
    protected $user;
    protected $vendorId;
    protected $roleId;


    public function __construct(Application $application)
    {
        if ($application->getUser()) {
            $this->user = $application->getUser();
            $this->userId = $this->user->pk_int_user_id;
            $this->roleId = $this->user->int_role_id;
            $this->vendorId = $application->getVendorDataId();
        }
    }

    public function leadAnalytics(Request $request)
    {
        Log::info('Lead analytics request from the mobile app:-', ['requests' => $request->all()]);

        $assignedStafId = AgentStaff::where('agent_id', $this->userId)->pluck('staff_id')->toArray();

        $response = Enquiry::where('tbl_enquiries.fk_int_user_id', $this->vendorId)
            ->leftJoin('tbl_feedback_status', 'tbl_feedback_status.pk_int_feedback_status_id', 'feedback_status')
            ->select(DB::raw('IFNULL(tbl_feedback_status.pk_int_feedback_status_id,0) as id'), DB::raw("COUNT(*) as count"), 'vchr_color as color', 'tbl_enquiries.created_at', DB::raw('IFNULL(tbl_feedback_status.vchr_status,"Yet to Contact") as status'))
            ->where(function ($q) use ($request) {
                $request->date_filter == "month" ? $q->whereMonth('tbl_enquiries.created_at', Carbon::now()->month) : '';
                $request->date_filter == "today" ? $q->whereDate('tbl_enquiries.created_at', Carbon::today()) : '';
                $request->date_filter == "week" ? $q->whereBetween('tbl_enquiries.created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]) : '';
                $request->date_filter == "custom" && $request->date_from != null && $request->date_to != null ? $q->whereBetween('tbl_enquiries.created_at', [$request->date_from . " 00:00:00", $request->date_to . " 23:59:59"]) : '';
            })
            ->groupBy(DB::raw("id"))
            ->orderBy('count', 'DESC')
            ->get();

        $enquiries_query = Enquiry::where('tbl_enquiries.fk_int_user_id', $this->vendorId);

        if ($this->roleId == User::STAFF) {
            if ($this->user->is_co_admin == 0 || $request->calling_mode == 1)
                $enquiries_query = $enquiries_query->where(function ($where) use ($assignedStafId) {
                    $where->where('staff_id', $this->userId)->orWhereIn('staff_id', $assignedStafId);
                });
        }
        if ($request->staff_id)
            $enquiries_query = $enquiries_query->whereIn('staff_id', $request->staff_id);

        if ($request->enquiry_source_id)
            $enquiries_query = $enquiries_query->whereIn('fk_int_enquiry_type_id', $request->enquiry_source_id);

        // Start the base query
        $enquiriesQuery = (clone $enquiries_query)
            ->select(
                DB::raw('COUNT(*) as total_count'),
                DB::raw("SUM(CASE WHEN DATE(tbl_enquiries.created_at) = CURDATE() THEN 1 ELSE 0 END) as today_count"),
                DB::raw("SUM(CASE WHEN tbl_enquiries.created_at BETWEEN '" . Carbon::now()->startOfWeek() . "' AND '" . Carbon::now()->endOfWeek() . "' THEN 1 ELSE 0 END) as this_week_count"),
                DB::raw("SUM(CASE WHEN tbl_enquiries.created_at BETWEEN '" . Carbon::now()->startOfMonth() . "' AND '" . Carbon::now() . "' THEN 1 ELSE 0 END) as this_month_count")
            );

        // Execute the query and get the first result as the counts are aggregated
        $counts = $enquiriesQuery->first();

        $data['status-wise-graph'] = $response;
        $data['leads-count'] = $counts;

        return $this->response(200, false, null, $data);
    }

    public function taskAnalytics(Request $request)
    {
        $tasks = Task::where('vendor_id', $this->vendorId)
            ->where(function ($q) {
                if (request('task_category'))
                    $q->where('task_category_id', request('task_category'));
            })
            ->where(function ($q) {
                if ($this->roleId != User::USERS && $this->user->is_co_admin == 0) {
                    $assignedStaffIds = AgentStaff::where('agent_id', $this->userId)->pluck('staff_id')->toArray();
                    $q->where(function ($query) use ($assignedStaffIds) {
                        $query->where('assigned_to', $this->userId)
                            ->orWhereIn('assigned_to', $assignedStaffIds);
                    });
                }
            })
            ->where(function ($q) use ($request) {
                $request->date_filter == "month" ? $q->whereMonth('created_at', Carbon::now()->month) : '';
                $request->date_filter == "today" ? $q->whereDate('created_at', Carbon::today()) : '';
                $request->date_filter == "week" ? $q->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]) : '';
                $request->date_filter == "custom" && $request->date_from != null && $request->date_to != null ? $q->whereBetween('created_at', [$request->date_from . " 00:00:00", $request->date_to . " 23:59:59"]) : '';

            })
            ->selectRaw('COUNT(*) as total_count')
            ->selectRaw('SUM(CASE WHEN status = 1 OR scheduled_date is NULL THEN 1 ELSE 0 END) as completed')
            ->selectRaw('SUM(CASE WHEN status = 0 AND scheduled_date <= CURDATE() OR scheduled_date is NULL THEN 1 ELSE 0 END) as overDue')
            ->selectRaw('SUM(CASE WHEN status = 0 AND scheduled_date > CURDATE() THEN 1 ELSE 0 END) as pending')
            ->first();

        $deal_tasks = DealTask::where('vendor_id', $this->vendorId)
            ->where(function ($q) {
                if (request('task_category'))
                    $q->where('task_category_id', request('task_category'));
            })
            ->where(function ($q) {
                if ($this->roleId != User::USERS && $this->user->is_co_admin == 0) {
                    $assignedStaffIds = AgentStaff::where('agent_id', $this->userId)->pluck('staff_id')->toArray();
                    $q->where(function ($query) use ($assignedStaffIds) {
                        $query->where('assigned_to', $this->userId)
                            ->orWhereIn('assigned_to', $assignedStaffIds);
                    });
                }
            })
            ->where(function ($q) use ($request) {
                $request->date_filter == "month" ? $q->whereMonth('created_at', Carbon::now()->month) : '';
                $request->date_filter == "today" ? $q->whereDate('created_at', Carbon::today()) : '';
                $request->date_filter == "week" ? $q->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]) : '';
                $request->date_filter == "custom" && $request->date_from != null && $request->date_to != null ? $q->whereBetween('created_at', [$request->date_from . " 00:00:00", $request->date_to . " 23:59:59"]) : '';

            })
            ->selectRaw('COUNT(*) as total_count')
            ->selectRaw('SUM(CASE WHEN status = 1 OR scheduled_date is NULL THEN 1 ELSE 0 END) as completed')
            ->selectRaw('SUM(CASE WHEN status = 0 AND scheduled_date <= CURDATE() OR scheduled_date is NULL THEN 1 ELSE 0 END) as overDue')
            ->selectRaw('SUM(CASE WHEN status = 0 AND scheduled_date > CURDATE() THEN 1 ELSE 0 END) as pending')
            ->first();

        // Merging the results
        $analytics = [
            'total_count' => $tasks->total_count + $deal_tasks->total_count,
            'completed' => $tasks->completed + $deal_tasks->completed,
            'overDue' => $tasks->overDue + $deal_tasks->overDue,
            'pending' => $tasks->pending + $deal_tasks->pending,
        ];

        $data['task-analytics'] = $analytics;


        return $this->response(200, false, null, $data);
    }

    public function dealAnalytics(Request $request)
    {
        $assignedStafId = AgentStaff::where('agent_id', $this->userId)->pluck('staff_id')->toArray();
        $deals = Deal::where(function ($query) use ($assignedStafId) {
            if ($this->roleId == Variables::STAFF && $this->user->is_co_admin == 0) {
                $query->where('agent_id', $this->userId)
                    ->orwhere('created_by', $this->userId);
                $query->where(function ($q) use ($assignedStafId) {
                    if ($assignedStafId)
                        $q->where('created_by', $assignedStafId)
                            ->orwhereIn('agent_id', $assignedStafId);
                });
            } elseif ($this->roleId == Variables::USER || $this->user->is_co_admin == 1) {
                $query->where('vendor_id', $this->vendorId);
            }
        })
            ->leftJoin('deal_stages', 'deal_stages.pk_int_deal_stage_id', 'deals.deal_stage_id')
            ->leftJoin('deal_categories as category', 'category.id', '=', 'deal_stages.deal_category_id')
            ->where(function ($q) use ($request) {
                $request->date_filter == "month" ? $q->whereMonth('deals.created_at', Carbon::now()->month) : '';
                $request->date_filter == "today" ? $q->whereDate('deals.created_at', Carbon::today()) : '';
                $request->date_filter == "week" ? $q->whereBetween('deals.created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]) : '';
                $request->date_filter == "custom" && $request->date_from != null && $request->date_to != null ? $q->whereBetween('deals.created_at', [$request->date_from . " 00:00:00", $request->date_to . " 23:59:59"]) : '';
            })
            ->groupBy('deal_category_id')
            ->select(
                DB::raw("ROUND(SUM(deal_amount)) as total"),
                DB::raw("COUNT(deal_category_id) as count"),
                'deal_category_id as deal_category_id', 'category.name as stage_name'
            )
            ->get();

        $response = Deal::where('deals.vendor_id', $this->vendorId)
            ->leftJoin('deal_stages', 'deal_stages.pk_int_deal_stage_id', 'deals.deal_stage_id')
            ->select(DB::raw('IFNULL(deal_stages.pk_int_deal_stage_id,0) as id'), DB::raw("COUNT(*) as count"), DB::raw("SUM(deal_amount) as total_amount"), DB::raw('IFNULL(deal_stages.deal_stage_name,"Yet to Contact") as status'))
            ->where(function ($q) use ($request) {
                $request->date_filter == "month" ? $q->whereMonth('deals.created_at', Carbon::now()->month) : '';
                $request->date_filter == "today" ? $q->whereDate('deals.created_at', Carbon::today()) : '';
                $request->date_filter == "week" ? $q->whereBetween('deals.created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]) : '';
                $request->date_filter == "custom" && $request->date_from != null && $request->date_to != null ? $q->whereBetween('deals.created_at', [$request->date_from . " 00:00:00", $request->date_to . " 23:59:59"]) : '';
            })
            ->groupBy(DB::raw("id"))
            ->orderBy('count', 'DESC')
            ->get();


        $data['deals-analytics'] = $deals;
        $data['status-deals-analytics'] = $response;

        return $this->response(200, false, null, $data);
    }
}
