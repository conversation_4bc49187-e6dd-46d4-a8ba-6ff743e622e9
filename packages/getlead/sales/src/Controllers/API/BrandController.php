<?php

namespace Getlead\Sales\Controllers\API;

use App\Facades\FileUpload;
use Getlead\Sales\Helper;
use Illuminate\Http\Request;
use Getlead\Sales\Controllers\Controller;
use Getlead\Sales\Models\Brand;
use Getlead\Sales\Resources\BrandResource;
use Validator;
use Auth;
use App\User;

class BrandController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    protected $vendorId,$commonObj;
    protected $imagePath;
    public function __construct(Request $request)
    {
        if($request->segment(1)=="api"){
            try{
                \JWTAuth::parseToken()->authenticate();
            }catch (\Exception $exp){
                abort(401);
            }
        }
        $this->vendorId = User::getVendorIdApi($request?->user()?->pk_int_user_id);
        $this->commonObj = new Helper();
        $this->imagePath = '/uploads/Brand/';
    }
    /**
     * @OA\Get(
     *     security={{"bearerAuth":{}}},
     *     path="/api/sales/brands",
     *     tags={"Brand List"},
     *     summary="Brand List",
     *     operationId="BrandList",
     *     @OA\Response(response=200,description="Success",@OA\MediaType(mediaType="application/json",)),
     *     @OA\Response(response=401,description="Unauthenticated"),
     *     @OA\Response(response=400,description="Bad Request"),
     *     @OA\Response(response=404,description="Not found"),
     *     @OA\Response(response=403,description="Forbidden"))
     * */
    public function index(Request $request)
    {
        $brands = Brand::where('fk_int_user_id',$this->vendorId)->get();
        return $this->response(200, false, null,BrandResource::collection($brands));
    }

    /**
     * @OA\Post(
     *     security={{"bearerAuth":{}}},
     *     path="/api/sales/brands/store",
     *     tags={"Create Brand"},
     *     summary="Create Brand",
     *     operationId="CreateBrand",
     *     @OA\Parameter(name="name",in="query",required=true,@OA\Schema(type="string")),
     *     @OA\Parameter(name="image",in="query",required=false,@OA\Schema(type="file")),
     *     @OA\Response(response=200,description="Success",@OA\MediaType(mediaType="application/json",)),
     *     @OA\Response(response=401,description="Unauthenticated"),
     *     @OA\Response(response=400,description="Bad Request"),
     *     @OA\Response(response=404,description="Not found"),
     *     @OA\Response(response=403,description="Forbidden"))
     * */
    public function store(Request $request)
    {
        $validate_fields = [
            'name' => ['required'],
            'image' => ['mimes:jpg,png']
        ];
        $validation_messages = [
        ];
        $validation_failed = $this->requestValidate($request,$validate_fields,$validation_messages);
        if ($validation_failed) {
        return $validation_failed;
        }
        else{
            $slug = $this->commonObj->slugify($request->name);
            $brand = new Brand();
            $brand->fk_int_user_id =  $this->vendorId;
            $brand->slug =  $slug;
            $brand->name =  $request->name;
            $brand->created_by = $request->parent_id;

            if($request->hasFile('image')){
                $image = $request->file('image');
                $name = mt_rand().'.'.$image->getClientOriginalExtension();
                FileUpload::uploadFile($image, $this->imagePath,$name,'s3');
                $brand->image = $name; 
            }
            $flag=$brand->save();
            if($flag)
                return $this->response(200, false, 'Brand Added');
            else
                return $this->response(200, true, 'Failed to add Brand, Please try again');

        }
    }
    /**
     * @OA\Get(
     *     security={{"bearerAuth":{}}},
     *     path="/api/sales/brands/activate/{id}",
     *     tags={"Activate Brand"},
     *     summary="Activate Brand",
     *     operationId="ActivateBrand",
     *     @OA\Parameter(name="id",in="path",required=true,@OA\Schema(type="integer")),
     *     @OA\Response(response=200,description="Success",@OA\MediaType(mediaType="application/json",)),
     *     @OA\Response(response=401,description="Unauthenticated"),
     *     @OA\Response(response=400,description="Bad Request"),
     *     @OA\Response(response=404,description="Not found"),
     *     @OA\Response(response=403,description="Forbidden"))
     * */
    public function activate($id)
    {

        $flag=Brand::where('id',$id)->where('fk_int_user_id',$this->vendorId)->update(['status'=>1]);
        if($flag)
            return $this->response(200, false, 'Brand Activated Successfully');
        else
            return $this->response(200, true, 'Failed to activate Brand, Please try again');

    }

    /**
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
     /**
     * @OA\Get(
     *     security={{"bearerAuth":{}}},
     *     path="/api/sales/brands/deactivate/{id}",
     *     tags={"Deactivate Brand"},
     *     summary="Deactivate Brand",
     *     operationId="DeactivateBrand",
     *     @OA\Parameter(name="id",in="path",required=true,@OA\Schema(type="integer")),
     *     @OA\Response(response=200,description="Success",@OA\MediaType(mediaType="application/json",)),
     *     @OA\Response(response=401,description="Unauthenticated"),
     *     @OA\Response(response=400,description="Bad Request"),
     *     @OA\Response(response=404,description="Not found"),
     *     @OA\Response(response=403,description="Forbidden"))
     * */
    public function deactivate($id)
    {
        $flag=Brand::where('id',$id)->where('fk_int_user_id',$this->vendorId)->update(['status'=>0]);
        if($flag)
            return $this->response(200, false, 'Brand Deactivated Successfully');
        else
            return $this->response(200, true, 'Failed to deactivate Brand, Please try again');
    }
   
     /**
     * @OA\Get(
     *     security={{"bearerAuth":{}}},
     *     path="/api/sales/brands/show/{id}",
     *     tags={"Show Brand"},
     *     summary="Show Brand",
     *     operationId="ShowBrand",
     *     @OA\Parameter(name="id",in="path",required=true,@OA\Schema(type="integer")),
     *     @OA\Response(response=200,description="Success",@OA\MediaType(mediaType="application/json",)),
     *     @OA\Response(response=401,description="Unauthenticated"),
     *     @OA\Response(response=400,description="Bad Request"),
     *     @OA\Response(response=404,description="Not found"),
     *     @OA\Response(response=403,description="Forbidden"))
     * */
    public function show($id)
    {
        $brand = Brand::where('fk_int_user_id',$this->vendorId)->where('id',$id)->first();
        if($brand)
            return $this->response(200, false, 'Brand Details Loaded', new BrandResource($brand));
        else
            return $this->response(200, true, 'Brand does not exist');
    }

   
     /**
     * @OA\Post(
     *     security={{"bearerAuth":{}}},
     *     path="/api/sales/brands/update/{id}",
     *     tags={"Update Brand"},
     *     summary="Update Brand",
     *     operationId="UpdateBrand",
     *     @OA\Parameter(name="id",in="path",required=false,@OA\Schema(type="integer")),
     *     @OA\Parameter(name="name",in="query",required=true,@OA\Schema(type="string")),
     *     @OA\Parameter(name="image",in="query",required=false,@OA\Schema(type="file")),
     *     @OA\Parameter(name="parent_id",in="query",required=true,@OA\Schema(type="integer")),
     *     @OA\Response(response=200,description="Success",@OA\MediaType(mediaType="application/json",)),
     *     @OA\Response(response=401,description="Unauthenticated"),
     *     @OA\Response(response=400,description="Bad Request"),
     *     @OA\Response(response=404,description="Not found"),
     *     @OA\Response(response=403,description="Forbidden"))
     * */
    public function update(Request $request, $id)
    {
        $validate_fields = [
            'name' => ['required'],
            'image' => ['mimes:jpg,png']
        ];
        $validation_messages = [
        ];
        $validation_failed = $this->requestValidate($request,$validate_fields,$validation_messages);
        if ($validation_failed) {
        return $validation_failed;
        }
        else{
            $brand = Brand::where('fk_int_user_id',$this->vendorId)->where('id',$id)->first();
            if($Brand){
                $slug = $this->commonObj->slugify($request->name);
                $brand = Brand::find($id);
                $brand->slug =  $slug;
                $brand->name =  $request->name;
                $brand->created_by = $request->parent_id;
        
                if($request->hasFile('image')){
                    $image = $request->file('image');
                    $name = mt_rand().'.'.$image->getClientOriginalExtension();
                    FileUpload::uploadFile($image, $this->imagePath,$name,'s3');
                    $brand->image = $name; 
                }
                $flag=$brand->save();
                if($flag)
                    return $this->response(200, false, 'Brand Updated');
                else
                    return $this->response(200, true, 'Failed to updated Brand, Please try again');
            }
            else
                return $this->response(200, true, 'Brand does not exist');
            

        }
    }
    /**
     * @OA\Get(
     *     security={{"bearerAuth":{}}},
     *     path="/api/sales/brands/delete/{id}",
     *     tags={"Delete Brand"},
     *     summary="Delete Brand",
     *     operationId="DeleteBrand",
     *     @OA\Parameter(name="id",in="path",required=true,@OA\Schema(type="integer")),
     *     @OA\Response(response=200,description="Success",@OA\MediaType(mediaType="application/json",)),
     *     @OA\Response(response=401,description="Unauthenticated"),
     *     @OA\Response(response=400,description="Bad Request"),
     *     @OA\Response(response=404,description="Not found"),
     *     @OA\Response(response=403,description="Forbidden"))
     * */
    public function destroy($id)
    {
        $brand = Brand::where('fk_int_user_id',$this->vendorId)->where('id',$id)->first();
        if($brand){
            $deleted = Brand::where('fk_int_user_id',$this->vendorId)->where('id',$id)->delete();
            if($deleted)
                return $this->response(200, false, 'Brand Deleted');
            else
                return $this->response(200, true, 'Failed to delete Brand, Please try again');
        }else
            return $this->response(200, true, 'Brand does not exist');
    }
}
