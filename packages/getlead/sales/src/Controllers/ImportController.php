<?php

namespace Getlead\Sales\Controllers;

use Getlead\Sales\Helper;
use Illuminate\Http\Request;
use Getlead\Sales\Controllers\Controller;
use Getlead\Sales\Models\Brand;
use Getlead\Sales\Resources\BrandResource;
use Validator;
use Auth;
use App\User;

class ImportController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    protected $vendorId,$commonObj;
    protected $imagePath;
    public function __construct()
    {
        $this->vendorId = User::getVendorId();
        $this->commonObj = new Helper();
    }
    public function index(Request $request)
    {
        return view('sales::import.index');
    }

}
