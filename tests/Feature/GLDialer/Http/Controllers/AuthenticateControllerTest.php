<?php

declare(strict_types=1);

namespace Tests\Feature\GLDialer\Http\Controllers;

use App\Common\Facades\Mediator;
use App\Subscription\Usecases\GetSubscription\GetSubscription;
use App\Subscription\Usecases\GetSubscription\Subscription;
use App\User;
use Shared\ValueObjects\PhoneNumber;
use Tests\FakePhoneNumber;
use Tests\TestCase;

final class AuthenticateControllerTest extends TestCase
{
    /**
     * @test
     */
    public function it_returns_error_response_when_credentials_are_invalid(): void
    {
        //existing user
        User::factory()->create();

        $phoneNumber = FakePhoneNumber::create('IN');

        $this->postJson(route('dialer.login'), [
            'phone_number' => $phoneNumber->toE164PhoneNumber(),
            'password' => 'wrong_password',
        ])->assertUnauthorized()
            ->assertJson([]);
    }

    /**
     * @test
     */
    public function it_returns_error_when_user_is_inactive(): void
    {
        //existing user
        $user = User::factory()->create();

        $this->postJson(route('dialer.login'), [
            'phone_number' => (new PhoneNumber($user->vchr_user_mobile))->toE164PhoneNumber(),
            'password' => 'password',
        ])->assertForbidden()
            ->assertJson([]);
    }

    /**
     * @test
     */
    public function it_returns_error_when_subscription_is_not_active(): void
    {
        //existing user
        $user = User::factory()->create();

        Mediator::fake(GetSubscription::class, Subscription::empty());

        $this->postJson(route('dialer.login'), [
            'phone_number' => (new PhoneNumber($user->vchr_user_mobile))->toE164PhoneNumber(),
            'password' => 'password',
        ])->assertForbidden()
            ->assertJson([]);

        Mediator::assertDispatched(
            static fn (GetSubscription $request) => $request->vendorId === $user->getBusinessId()
        );
    }

    /**
     * @test
     */
    public function it_returns_successful_response_with_token(): void
    {
        //existing user
        $user = User::factory()->create();

        Mediator::fake(GetSubscription::class, new Subscription(['CRM']));

        $response = $this->postJson(route('dialer.login'), [
            'phone_number' => (new PhoneNumber($user->vchr_user_mobile))->toE164PhoneNumber(),
            'password' => 'password',
        ]);

        $response->assertOk();
        $responseDecoded = json_decode($response->getContent(), true);
        $this->assertEquals($user->getBusinessId(), $responseDecoded['vendor_id']);
        $this->assertEquals($user->vchr_user_name, $responseDecoded['name']);
        $this->assertEquals($user->email, $responseDecoded['email']);
        $this->assertArrayHasKey('token', $responseDecoded);
        $this->assertNotEmpty($responseDecoded['token']);

        Mediator::assertDispatched(
            static fn (GetSubscription $request) => $request->vendorId === $user->getBusinessId()
        );
    }
}
